; 测试版专用安装脚本

!macro preInit
  ; 强制设置测试版安装目录
  SetRegView 64
  WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-test"
  WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-test"
  SetRegView 32
  WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-test"
  WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-test"
!macroend

!macro customInit
  ; 强制设置测试版安装目录
  StrCpy $INSTDIR "$PROGRAMFILES\popofifi-test"
!macroend
