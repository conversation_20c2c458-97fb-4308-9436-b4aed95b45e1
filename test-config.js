// 测试配置脚本 - 验证不同模式下的应用名称和路径
const { app } = require('electron');

// 模拟不同的构建模式
const testModes = ['test', 'prod'];

testModes.forEach(mode => {
  console.log(`\n=== 测试模式: ${mode} ===`);
  
  // 设置环境变量
  process.env.MODE = mode;
  
  // 根据模式设置应用名称
  if (mode === 'test') {
    app.setName('popofifi-test');
  } else {
    app.setName('popofifi-prod');
  }
  
  console.log(`应用名称: ${app.getName()}`);
  console.log(`预期的userData路径: ${app.getPath('userData')}`);
});

// 退出应用
app.quit();
