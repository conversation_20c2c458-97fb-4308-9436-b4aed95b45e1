const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const os = require('os')

const defaultConfig = {
    "APP":{
        "style": "",
        "alwaysOnTop": false,
        "fullScreen": false,
        "name": "",
        "pwd": ""
    }
}

let _config = JSON.parse(JSON.stringify(defaultConfig))

const getConfigPath = () => {
    console.log('isPageaged', app.isPackaged)
  if (app.isPackaged) {
    // 打包后，config.json 文件在 exe 文件的同级目录下
    const configDir = app.getPath('userData');
    const configPath = path.join(configDir+'-screen' + import.meta.env.MODE, 'config.json');
    return configPath
  } else {
    let _path = path.join(app.getAppPath(), './config.json');
    console.log(_path)
    // 开发环境下，config.json 文件在项目根目录下
    return _path
  }
};

//获取本机ip地址
function getIPAdress() {
    var interfaces = os.networkInterfaces();　　
    let ips = []
    for (var devName in interfaces) {　　　　
        var iface = interfaces[devName];　　　　　　
        for (var i = 0; i < iface.length; i++) {
            var alias = iface[i];
            if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
                ips.push(alias.address);
            }
        }　　
    }
    return ips
}
const saveConfig = () =>{
    let __config = JSON.parse(JSON.stringify(config))
    __config.IPs = undefined
    const configPath = getConfigPath();
    fs.writeFileSync(configPath, JSON.stringify(__config, null, 2));
}
const readConfig = () => {
  const configPath = getConfigPath();
  try {
    const configData = fs.readFileSync(configPath, 'utf-8');
    let json = JSON.parse(configData);
    _config = json
    json.IPs = getIPAdress()
    Object.assign(_config, json)
    return _config
  } catch (error) {
    console.error('Error reading config file');
    return null
  }
};

const setLocalConfig = (key, value) => {
    switch(key){
      case 'name':
        _config.APP.name = value
        break;
      default:
        return
    }
    saveConfig()
}

let config = readConfig();
if (!config){
    config = _config
    saveConfig()
}
console.log(config);

export default config
export {setLocalConfig}