; 生产版专用安装脚本

!macro preInit
  ; 强制设置生产版安装目录
  SetRegView 64
  WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-prod"
  WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-prod"
  SetRegView 32
  WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-prod"
  WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-prod"
!macroend

!macro customInit
  ; 强制设置生产版安装目录
  StrCpy $INSTDIR "$PROGRAMFILES\popofifi-prod"
!macroend
